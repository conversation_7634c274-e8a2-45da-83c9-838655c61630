import { v4 as uuidv4 } from "uuid";
import {
  basePriceComponent,
  discountPriceComponent,
  informationPriceComponent,
  invoiceCodeConstant,
  invoiceMetaData,
  taxCGSTPriceComponent,
  taxSGSTPriceComponent,
  taxIGSTPriceComponent,
} from "../../../utils/fhir.constants.js";

export const generateInvoiceResource = async (
  invoice,
  patientResource,
  practitionerResources,
  organizationResource,
  chargeItemResources,
  currentTime
) => {
  const id = uuidv4();

  return {
    fullUrl: `urn:uuid:${id}`,
    resource: {
      resourceType: "Invoice",
      id,
      meta: invoiceMetaData(currentTime),
      status: "issued",
      type: invoiceCodeConstant(),
      date: invoice.date,
      identifier: [
        {
          value: invoice.id,
        },
      ],
      totalNet: invoice.totalNet,
      totalGross: invoice.totalGross,
      subject: {
        reference: patientResource.fullUrl,
        display: "Patient",
      },

      lineItem: invoice.lineItem.map((Item, index) => {
        const matchingChargeItem = chargeItemResources.find((resource) => {
          console.log(
            "Found matching chargeItemResource",
            resource.resource.productCodeableConcept,
            Item
          );
          return (
            resource.resource.productCodeableConcept.text?.toLowerCase() ===
            Item.type?.toLowerCase()
          );
        });

        return {
          sequence: index + 1,
          chargeItemReference: matchingChargeItem
            ? {
                reference: `${matchingChargeItem.fullUrl}`,
                display: "Charge Item",
              }
            : undefined,
          priceComponent: Item.priceComponent?.map((component) => ({
            type: component.type === "information" ? "informational" : component.type,
            code:
              component.type === "base"
                ? basePriceComponent()
                : component.type === "information"
                ? informationPriceComponent()
                : component.type === "discount"
                ? discountPriceComponent()
                : component.type === "tax" && component.display === "CGST"
                ? taxCGSTPriceComponent()
                : component.type === "tax" && component.display === "SGST"
                ? taxSGSTPriceComponent()
                : component.type === "tax" && component.display === "IGST"
                ? taxIGSTPriceComponent()
                : {},
            amount: component.amount,
          })),
        };
      }),
      participant: practitionerResources.map((practitioner) => ({
        actor: { reference: practitioner.fullUrl, display: "Practitioner" },
      })),
      issuer: { reference: organizationResource.fullUrl },
    },
  };
};

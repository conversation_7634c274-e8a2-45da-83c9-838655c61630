import { v4 as uuidv4 } from "uuid";
import {documentReferenceMetadata, getSnomedCtCode, documentReferenceDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateDocumentReferenceResource = async (status, docStatus, type, content, patientResource) => {
    const id = uuidv4();

    // Handle special cases where SNOMED codes are not available
    let typeCodeableConcept;
    if (type === "Invoice Record") {
        // Invoice Record uses fixed text as per ABDM specification
        typeCodeableConcept = {
            text: "Invoice Record"
        };
    } else {
        // For other types, try to get SNOMED code
        try {
            const getSnomedData = await generateSnomedCtCode(type);
            typeCodeableConcept = getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term);
        } catch (error) {
            console.warn(`SNOMED code not found for type: ${type}, using text only`);
            typeCodeableConcept = {
                text: type
            };
        }
    }

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: 'DocumentReference',
            id,
            meta: documentReferenceMetadata(),
            status,
            docStatus,
            type: typeCodeableConcept,
            subject: {
                reference: `urn:uuid:${patientResource.resource.id}`,
                display: patientResource.resource.resourceType
            },
            content: content.map(({ attachment }) => ({
                attachment: {
                    contentType: attachment.contentType,
                    language: 'en',
                    data: attachment.data,
                    title: toTitleCase(attachment.title.trim()),
                    creation: attachment.creation
                }
            }))
            // text: documentReferenceDiv()
        }
    }
}
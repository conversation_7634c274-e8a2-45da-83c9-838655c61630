import { v4 as uuidv4 } from "uuid";
import {documentReferenceMetadata, getSnomedCtCode, documentReferenceDiv } from "../../../utils/fhir.constants.js";
import { toTitleCase } from "../../../utils/titlecase.generator.js";
import { generateSnomedCtCode } from "./snomed_ct_code.generator.fhir.js";

export const generateDocumentReferenceResource = async (
    currentTime,
    patientResource,
    practitionerResource,
    binaryResource,
    type = "Invoice Record",
    title = "Invoice Report"
) => {
    const id = uuidv4();

    // Use proper SNOMED code for Invoice Record as per ABDM specification
    let typeCodeableConcept;
    if (type === "Invoice Record") {
        // Use SNOMED code for Invoice/Bill as per ABDM examples
        typeCodeableConcept = {
            coding: [
                {
                    system: "http://snomed.info/sct",
                    code: "371530004",
                    display: "Clinical consultation report"
                }
            ],
            text: "Invoice Record"
        };
    } else {
        // For other types, try to get SNOMED code
        try {
            const getSnomedData = await generateSnomedCtCode(type);
            typeCodeableConcept = getSnomedCtCode(getSnomedData.conceptId, getSnomedData.term);
        } catch (error) {
            console.warn(`SNOMED code not found for type: ${type}, using text only`);
            typeCodeableConcept = {
                text: type
            };
        }
    }

    return {
        fullUrl: `urn:uuid:${id}`,
        resource: {
            resourceType: "DocumentReference",
            id,
            meta: {
                versionId: "1",
                lastUpdated: currentTime,
                profile: [
                    "https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentReference"
                ]
            },
            text: {
                status: "generated",
                div: `<div xmlns="http://www.w3.org/1999/xhtml"><p><b>Generated Narrative: DocumentReference ${id}</b></p><p><b>status</b>: current</p><p><b>docStatus</b>: final</p><p><b>type</b>: ${type}</p><p><b>subject</b>: <a href="#Patient-${patientResource.resource.id}">${patientResource.resource.name?.[0]?.text || 'Patient'}</a></p><blockquote><p><b>content</b></p><h3>Attachments</h3><table class="grid"><tr><td>-</td><td><b>ContentType</b></td><td><b>Language</b></td><td><b>Data</b></td><td><b>Title</b></td><td><b>Creation</b></td></tr><tr><td>*</td><td>application/pdf</td><td>English (Region=India)</td><td>(base64 data - ${Math.floor(binaryResource.resource.data.length * 0.75)} base64 chars)</td><td>${title}</td><td>${currentTime}</td></tr></table></blockquote></div>`
            },
            status: "current",
            docStatus: "final",
            type: typeCodeableConcept,
            subject: {
                reference: `Patient/${patientResource.resource.id}`,
                display: patientResource.resource.name?.[0]?.text || 'Patient'
            },
            author: [
                {
                    reference: `Practitioner/${practitionerResource.resource.id}`,
                    display: practitionerResource.resource.name?.[0]?.text || 'Practitioner'
                }
            ],
            content: [
                {
                    attachment: {
                        contentType: "application/pdf",
                        language: "en-IN",
                        data: binaryResource.resource.data,
                        title: title,
                        creation: currentTime
                    }
                }
            ]
        }
    };
};
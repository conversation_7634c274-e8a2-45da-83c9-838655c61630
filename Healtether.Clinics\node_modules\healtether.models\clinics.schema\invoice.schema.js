import mongoose from "mongoose";
import { CLIENT_COLLECTION, PAYMENT_COLLECTION, QUEUED_APPOINTMENT_COLLECTION } from "../mongodb.collection.name.js";

const invoiceSchema = new mongoose.Schema({
    invoiceNumber: {
        type: String,
        required: true,
        index: true
    },
    treatments: [
        {
            treatment: {
                type: String,
                required: true,
                maxlength: 255
            },
            quantity: {
                type: Number,
                required: true
            },
            amount: {
                type: mongoose.Types.Decimal128,
                required: true,
            },
            discRate: {
                type: mongoose.Types.Decimal128,
            },
            sgstRate: {
                type: mongoose.Types.Decimal128,
                default: 0.00,
            },
            cgstRate: {
                type: mongoose.Types.Decimal128,
                default: 0.00,
            },
            sgstAmount:{
                type: mongoose.Types.Decimal128,
                default: 0.00,
            },
            cgstAmount:{
                type: mongoose.Types.Decimal128,
                default: 0.00,
            },
            taxAmount: {
                type: mongoose.Types.Decimal128,
                default: 0.00,
            }
        },
    ],
    totalAmount: {
        type: mongoose.Types.Decimal128,
        required: true,
    }, 
    totalTax: {
        type: mongoose.Types.Decimal128,
        required: true,
    },
    totalCost: {
        type: mongoose.Types.Decimal128,
        required: true,
    },
    discountRate:{
        type: Number,
        default: 0,
    },
    discount:{
        type: mongoose.Types.Decimal128,
        default: 0.00,
    },
    paidAmount:{
        type: mongoose.Types.Decimal128,
        default: 0.00,
    },
    created: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    modified: {
        on: {
            type: Date,
            default: Date.Now
        },
        by: {
            id: String,
            name: {
                type: String,
                maxLength: 255
            },
        }
    },
    appointmentId:{
        type: mongoose.Schema.Types.ObjectId,
        ref: QUEUED_APPOINTMENT_COLLECTION
    },
    clinic:{
        type: mongoose.Schema.Types.ObjectId,
        ref: CLIENT_COLLECTION
    },
    
}, {versionKey: '1.0'});

invoiceSchema.virtual('payments', {
    ref: PAYMENT_COLLECTION,
    localField: '_id',
    foreignField: 'invoice'
});
//const Invoice = new mongoose.model("Invoice", invoiceSchema);
export {invoiceSchema};